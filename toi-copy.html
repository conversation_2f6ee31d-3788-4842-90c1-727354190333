<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1" />
    <title>TOI Oncology Pathways — Value Based Pathways Included</title>

    <!-- USAGE NOTES:
 - Save as pathways-accordion.html and open in a browser.
 - Change colors by editing :root CSS variables.
 - Replace the <ul> / paragraph bodies inside each .regimen-list or .subsection .body with exact PDF content where indicated.
 - Inputs control open/close (checkbox method). Labels have aria-controls; aria-expanded is static due to JS-free requirement (see comment in file).
 - Keyboard: Tab to a label/input and press Space to toggle.
-->

    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <link
      href="https://fonts.googleapis.com/css2?family=Roboto+Slab:wght@400;500;600;700&display=swap"
      rel="stylesheet"
    />

    <style>
      /* CSS variables (color tokens + spacing) */
      :root {
        --primary-blue: #336680;
        --primary-green: #2e5f33;
        --light-bg: #ffffff;
        --page-bg: #ffffff;
        --heading: #336680;
        --subtitle: #2e5f33;
        --paragraph: #1f3e4d;
        --icon: #2e5f33;
        --active: #2e5f33;
        --border: #e9ecef;
        --breadcrumb: #2e5f33;
        --content-color: #336680;

        --radius: 8px;
        --max-width: 1200px;
        --pad: 20px;
        --gap: 16px;
        --shadow-subtle: 0 2px 8px rgba(0, 0, 0, 0.08);
      }

      /* Base reset */
      * {
        box-sizing: border-box;
      }
      html,
      body {
        min-height: 100%;
      }
      body {
        margin: 0;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          "Helvetica Neue", Arial, sans-serif;
        background: var(--page-bg);
        color: var(--paragraph);
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        line-height: 1.6;
      }

      /* Header Styles */
      .header {
        background: var(--light-bg);
        border-bottom: 1px solid var(--border);
        padding: 16px 0;
      }

      .header-container {
        max-width: var(--max-width);
        margin: 0 auto;
        padding: 0 24px;
        display: flex;
        align-items: center;
        justify-content: space-between;
      }

      .logo {
        display: flex;
        align-items: center;
        gap: 12px;
      }

      .logo img {
        width: 40px;
        height: 40px;
        border-radius: 50%;
      }

      .logo-text {
        font-size: 16px;
        font-weight: 600;
        color: var(--heading);
      }

      /* Breadcrumb Styles */
      .breadcrumb-container {
        background: var(--light-bg);
        border-bottom: 1px solid var(--border);
        padding: 12px 0;
      }

      .breadcrumb {
        max-width: var(--max-width);
        margin: 0 auto;
        padding: 0 24px;
        display: flex;
        align-items: center;
        gap: 8px;
        font-family: "Inter", sans-serif;
        font-size: 13px;
        color: #2e5f33;
      }

      .breadcrumb a {
        color: #2e5f33;
        text-decoration: none;
        transition: color 0.2s;
        text-decoration: underline;
        font-weight: 500;
      }

      .breadcrumb-separator {
        color: #2e5f33;
        margin: 0 4px;
      }

      .breadcrumb-current {
        color: #2e5f33;
      }

      /* Main Content Container */
      .main-container {
        max-width: var(--max-width);
        margin: 0 auto;
        padding: 32px 18px;
      }

      /* Page Title and Description */
      .page-header {
        margin-bottom: 32px;
      }

      .page-title {
        font-family: "Roboto Slab", serif;
        font-size: 22px;
        font-weight: 700;
        color: #336680;
        margin: 0 0 16px 0;
        line-height: 1.2;
      }

      .page-description {
        font-family: "Inter", sans-serif;
        font-size: 14px;
        color: #1f3e4d;
        line-height: 1.6;
        margin: 0;
        max-width: 800px;
      }

      .accent-panel {
        background: linear-gradient(
          180deg,
          rgba(40, 74, 79, 0.98),
          rgba(40, 74, 79, 0.95)
        );
        margin-bottom: 14px;
        display: flex;
        gap: 12px;
        align-items: center;
      }
      .accent-panel .accent-title {
        font-weight: 700;
      }

      .callout {
        background: #f1fbfb;
        border-left: 4px solid rgba(40, 74, 79, 0.12);
        padding: 10px 12px;
        border-radius: 8px;
        color: var(--subtitle);
        margin-bottom: 12px;
        font-size: 13px;
      }

      /* Accordion core */
      .accordion {
        width: 100%;
        margin-bottom: 4px;
        border: none;
      }

      .accordion.level-1 {
        margin-bottom: 2px;
      }

      .accordion.level-2 {
        margin-left: 20px;
        margin-bottom: 2px;
      }

      .accordion.level-3 {
        margin-left: 40px;
        margin-bottom: 4px;
        border-bottom: 1px solid #e9ecef;
        padding-bottom: 8px;
      }
      /* hide but keep in accessibility tree */
      .accordion input[type="checkbox"] {
        position: absolute;
        opacity: 0;
        height: 1px;
        width: 1px;
        margin: 0;
        padding: 0;
        clip: rect(1px 1px 1px 1px);
      }

      .head {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 0;
        background: transparent;
        cursor: pointer;
        border: none;
        margin: 0;
        font-family: "Inter", sans-serif;
        font-size: 14px;
        color: #2e5f33;
      }

      .head:hover {
        background: transparent;
      }
      .head:focus,
      .head:focus-visible {
        outline: none;
      }

      .head h3 {
        margin: 0;
        color: #2e5f33;
        font-family: "Inter", sans-serif;
        font-weight: 400;
        flex: 1;
      }

      /* Level-specific font sizes and weights */
      .accordion.level-1 .head h3 {
        font-size: 15px;
        font-weight: 600;
      }

      .accordion.level-2 .head h3 {
        font-size: 14px;
        font-weight: 500;
      }

      .accordion.level-3 .head h3 {
        font-size: 13px;
        font-weight: 400;
      }

      .head .small-sub {
        color: var(--subtitle);
        font-size: 13px;
        margin-top: 2px;
      }

      /* Triangle arrow */
      .triangle {
        width: 0;
        height: 0;
        border-left: 6px solid #2e5f33;
        border-top: 4px solid transparent;
        border-bottom: 4px solid transparent;
        transition: transform 0.2s ease;
        margin-right: 8px;
      }

      /* Rotate triangle when expanded */
      .accordion input[type="checkbox"]:checked + .head .triangle {
        transform: rotate(90deg);
        border-left-color: #2e5f33;
      }

      .content {
        overflow: hidden;
        max-height: 0;
        transition: max-height 0.36s cubic-bezier(0.2, 0.9, 0.2, 1),
          padding 0.2s;
        padding: 0;
      }
      .accordion input[type="checkbox"]:checked ~ .content {
        max-height: 5000px;
        padding: 0 0 12px 0;
      }

      .panel-body {
        padding: 6px 0 6px 0;
        font-size: 14px;
      }

      /* nested visuals */
      .group {
        margin-top: 2px;
        padding: 4px;
      }
      .level-1 {
      }
      .level-2 {
        margin-left: 6px;
        padding: 8px;
      }
      .level-3 {
        margin-left: 12px;
        padding: 6px;
      }

      /* Sub-accordion content layout (two-column or single) */
      .sub-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
        align-items: start;
      }
      .sub-grid.three-col {
        grid-template-columns: 1fr 1fr 1fr;
      }

      .sub-col {
      }
      .sub-col h4 {
        margin: 0 0 8px 0;
        color: #336680;
        font-family: "Roboto Slab", serif;
        font-size: 12px;
        font-weight: 600;
      }
      .sub-col .lead {
        color: #336680;
        font-family: "Roboto Slab", serif;
        font-size: 12px;
        margin-bottom: 6px;
      }
      .sub-col ul {
        margin: 0;
        padding-left: 18px;
        color: #336680;
        font-family: "Roboto Slab", serif;
        font-size: 12px;
      }
      .sub-col li {
        margin: 6px 0;
      }

      /* for single column (Biliary), we reuse .sub-col inside content */
      .single-block {
      }

      .single-block h4 {
        margin: 0 0 8px 0;
        color: #336680;
        font-family: "Roboto Slab", serif;
        font-size: 12px;
        font-weight: 600;
      }

      .single-block .lead {
        color: #336680;
        font-family: "Roboto Slab", serif;
        font-size: 12px;
        margin-bottom: 6px;
      }

      .single-block ul {
        margin: 0;
        padding-left: 18px;
        color: #336680;
        font-family: "Roboto Slab", serif;
        font-size: 12px;
      }

      .single-block li {
        margin: 6px 0;
      }

      /* Notes styling */
      .notes {
            font-family: "Inter", sans-serif;
    font-weight: 400;
    font-size: 10px;
    color: #1F3E4D;
    margin: 12px 0;
    line-height: 1.2;
      }

      /* responsive */

      @media (max-width: 520px) {
        .sub-grid {
          grid-template-columns: 1fr;
        }
      }
      @media (max-width: 520px) {
        body {
          padding: 16px;
        }
        .card {
          padding: 14px;
        }
        .head {
          padding: 0.55rem 0.6rem;
        }
      }

      /* Parent section borders */
      .parent-border {
        border-top: 1px solid #E6E6E6;
        margin: 0;
      }
    </style>
  </head>
  <body>
    <!-- Header -->
    <header class="header">
      <div class="header-container">
        <div class="logo">
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" width="255px" height="79px" viewBox="0 0 255 79" enable-background="new 0 0 255 79" xml:space="preserve">  <image id="image0" width="255" height="79" x="0" y="0"
    href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAP8AAABPCAYAAAAkwhO/AAAABGdBTUEAALGPC/xhBQAAACBjSFJN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" />
</svg>

        </div>
      </div>
    </header>

    <!-- Breadcrumb -->
    <nav class="breadcrumb-container">
      <div class="breadcrumb">
        <a href="#" onclick="return false;">Pathways</a>
        <span class="breadcrumb-separator">></span>
        <a href="#" onclick="return false;">Value-based Pathways</a>
        <span class="breadcrumb-separator">></span>
        <span class="breadcrumb-current">Anal Cancer Pathways</span>
      </div>
    </nav>

    <!-- Main Content -->
    <main class="main-container">
      <div class="page-header">
        <h1 class="page-title">TOI Preferred Oncology Pathways & Formulary</h1>
        <p class="page-description">
          This page provides a structured view of TOI's evidence-based
          oncology pathways. Use the accordions below to browse specific disease
          areas, recommended regimens, and references.
        </p>
      </div>

      <!-- TOP LEVEL: Pathways (level-1) -->
      <section class="accordion level-1 parent-border" aria-label="Pathways">
        <input type="checkbox" id="lvl1-pathways" checked />
        <label
          class="head"
          for="lvl1-pathways"
          role="button"
          aria-controls="panel-lvl1-pathways"
          aria-expanded="false"
          tabindex="0"
        >
          <div class="triangle"></div>
          <h3>Pathways</h3>
        </label>

        <div
          class="content"
          id="panel-lvl1-pathways"
          role="region"
          aria-labelledby="lvl1-pathways"
        >
          <div class="panel-body">
            <!-- SUB-ACCORDION: Value Based Pathways (level-2) -->
            <div
              class="accordion group level-2 parent-border"
              aria-label="Value Based Pathways"
            >
              <input type="checkbox" id="lvl2-value" checked />
              <label
                class="head"
                for="lvl2-value"
                role="button"
                aria-controls="panel-lvl2-value"
                aria-expanded="false"
                tabindex="0"
              >
                <div class="triangle"></div>
                <h3>Value Based Pathways</h3>
              </label>

              <div
                class="content"
                id="panel-lvl2-value"
                role="region"
                aria-labelledby="lvl2-value"
              >
                <div class="panel-body">
                  <!-- --- Anal Cancer (level-3) --- -->
                  <div class="accordion group level-3" aria-label="Anal Cancer">
                    <input type="checkbox" id="lvl3-anal" />
                    <label
                      class="head"
                      for="lvl3-anal"
                      role="button"
                      aria-controls="panel-lvl3-anal"
                      aria-expanded="false"
                      tabindex="0"
                    >
                      <div class="triangle"></div>
                      <h3>Anal Cancer</h3>
                    </label>

                    <div
                      class="content"
                      id="panel-lvl3-anal"
                      role="region"
                      aria-labelledby="lvl3-anal"
                    >
                      <div class="panel-body">
                        <!-- Two-column grid: Localized | Advanced/Metastatic -->
                        <div class="sub-grid" aria-hidden="false">
                          <div
                            class="sub-col"
                            role="region"
                            aria-labelledby="anal-localized"
                          >
                            <h4 id="anal-localized">Localized Standard combined-modality</h4>
                            <ul class="regimen-list">
                              <li>Capecitabine + mitomycin + XRT</li>
                            </ul>
                          </div>

                          <div
                            class="sub-col"
                            role="region"
                            aria-labelledby="anal-advanced"
                          >
                            <h4 id="anal-advanced">
                              Advanced/Metastatic: 1st line Systemic therapy
                            </h4>
                            <ul class="regimen-list">
                              <li>Carboplatin + paclitaxel</li>
                            </ul>
                          </div>
                        </div>

                       
                      </div>
                    </div>
                  </div>

                  <!-- --- Biliary Tract Cancers (level-3) --- -->
                  <div
                    class="accordion group level-3"
                    aria-label="Biliary Tract Cancers"
                  >
                    <input type="checkbox" id="lvl3-biliary" />
                    <label
                      class="head"
                      for="lvl3-biliary"
                      role="button"
                      aria-controls="panel-lvl3-biliary"
                      aria-expanded="false"
                      tabindex="0"
                    >
                      <div class="triangle"></div>
                      <h3>
                        Biliary Tract Cancers (Gallbladder/Cholangiocarcinoma)
                      </h3>
                    </label>

                    <div
                      class="content"
                      id="panel-lvl3-biliary"
                      role="region"
                      aria-labelledby="lvl3-biliary"
                    >
                      <div class="panel-body">
                        <!-- Single-column block (as requested) -->
                        <div
                          class="single-block"
                          role="region"
                          aria-labelledby="biliary-1"
                        >
                          <h4 id="biliary-1">
                            Unresectable/Metastatic: 1st line
                          </h4>
                          <div class="lead">Recommended regimens</div>
                          <ul class="regimen-list">
                            <li>Durvalumab + gemcitabine + cisplatin</li>
                            <li>Gemcitabine + cisplatin</li>
                            <li>
                              Gemcitabine + carboplatin (cisplatin ineligible)
                            </li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- --- Cervical Cancer (level-3) --- -->
                  <div
                    class="accordion group level-3"
                    aria-label="Cervical Cancer"
                  >
                    <input type="checkbox" id="lvl3-cervical" />
                    <label
                      class="head"
                      for="lvl3-cervical"
                      role="button"
                      aria-controls="panel-lvl3-cervical"
                      aria-expanded="false"
                      tabindex="0"
                    >
                      <div class="left">
                        <div
                          style="
                            width: 6px;
                            height: 28px;
                            
                            margin-right: 8px;
                          "
                        ></div>
                        <div>
                          <h3>Cervical Cancer</h3>
                          <div class="small-sub">
                            Recurrent/Metastatic PD-L1 stratified
                          </div>
                        </div>
                      </div>
                      <svg class="chev" viewBox="0 0 24 24" aria-hidden="true">
                        <path
                          fill="currentColor"
                          d="M9.29 6.71a1 1 0 011.42 0L15 12l-4.29 4.29a1 1 0 01-1.42-1.42L12.17 12 9.29 8.12a1 1 0 010-1.41z"
                        />
                      </svg>
                    </label>

                    <div
                      class="content"
                      id="panel-lvl3-cervical"
                      role="region"
                      aria-labelledby="lvl3-cervical"
                    >
                      <div class="panel-body">
                        <!-- Two-column: PD-L1 positive vs PD-L1 negative -->
                        <div class="sub-grid">
                          <div
                            class="sub-col"
                            role="region"
                            aria-labelledby="cervical-pos"
                          >
                            <h4 id="cervical-pos">
                              Recurrent/Metastatic - PD-L1 positive: 1st line
                            </h4>
                            <ul class="regimen-list">
                              <li>Pembrolizumab ± cisplatin + paclitaxel</li>
                              <li>Pembrolizumab ± carboplatin + paclitaxel</li>
                            </ul>
                          </div>

                          <div
                            class="sub-col"
                            role="region"
                            aria-labelledby="cervical-neg"
                          >
                            <h4 id="cervical-neg">
                              Recurrent/Metastatic - PD-L1 negative: 1st line
                            </h4>
                            <ul class="regimen-list">
                              <li>Cisplatin + paclitaxel</li>
                              <li>Carboplatin + paclitaxel</li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- --- Chronic Lymphocytic Leukemia/Small Lymphocytic Lymphoma (CLL/SLL) (level-3) --- -->
                  <div
                    class="accordion group level-3"
                    aria-label="Chronic Lymphocytic Leukemia/Small Lymphocytic Lymphoma"
                  >
                    <input type="checkbox" id="lvl3-cll-sll" />
                    <label
                      class="head"
                      for="lvl3-cll-sll"
                      role="button"
                      aria-controls="panel-lvl3-cll-sll"
                      aria-expanded="false"
                      tabindex="0"
                    >
                      <div class="left">
                        <div
                          style="
                            width: 6px;
                            height: 28px;
                            
                            margin-right: 8px;
                          "
                        ></div>
                        <div>
                          <h3>
                            Chronic Lymphocytic Leukemia/Small Lymphocytic
                            Lymphoma (CLL/SLL)
                          </h3>
                          <div class="small-sub">
                            Any 17p/TP53 status: 1st line
                          </div>
                        </div>
                      </div>
                      <svg class="chev" viewBox="0 0 24 24" aria-hidden="true">
                        <path
                          fill="currentColor"
                          d="M9.29 6.71a1 1 0 011.42 0L15 12l-4.29 4.29a1 1 0 01-1.42-1.42L12.17 12 9.29 8.12a1 1 0 010-1.41z"
                        />
                      </svg>
                    </label>

                    <div
                      class="content"
                      id="panel-lvl3-cll-sll"
                      role="region"
                      aria-labelledby="lvl3-cll-sll"
                    >
                      <div class="panel-body">
                        <!-- Single-column block -->
                        <div
                          class="single-block"
                          role="region"
                          aria-labelledby="cll-sll-1"
                        >
                          <h4 id="cll-sll-1">Any 17p/TP53 status: 1st line</h4>
                          <ul class="regimen-list">
                            <li>Acalabrutinib</li>
                            <li>Zanubrutinib</li>
                          </li>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- --- Chronic Myeloid Leukemia (CML) (level-3) --- -->
                  <div
                    class="accordion group level-3"
                    aria-label="Chronic Myeloid Leukemia"
                  >
                    <input type="checkbox" id="lvl3-cml" />
                    <label
                      class="head"
                      for="lvl3-cml"
                      role="button"
                      aria-controls="panel-lvl3-cml"
                      aria-expanded="false"
                      tabindex="0"
                    >
                      <div class="left">
                        <div
                          style="
                            width: 6px;
                            height: 28px;
                            
                            margin-right: 8px;
                          "
                        ></div>
                        <div>
                          <h3>Chronic Myeloid Leukemia (CML)</h3>
                          <div class="small-sub">
                            Chronic phase treatment options
                          </div>
                        </div>
                      </div>
                      <svg class="chev" viewBox="0 0 24 24" aria-hidden="true">
                        <path
                          fill="currentColor"
                          d="M9.29 6.71a1 1 0 011.42 0L15 12l-4.29 4.29a1 1 0 01-1.42-1.42L12.17 12 9.29 8.12a1 1 0 010-1.41z"
                        />
                      </svg>
                    </label>

                    <div
                      class="content"
                      id="panel-lvl3-cml"
                      role="region"
                      aria-labelledby="lvl3-cml"
                    >
                      <div class="panel-body">
                        <!-- Two-column: Low risk vs Intermediate/High risk -->
                        <div class="sub-grid">
                          <div
                            class="sub-col"
                            role="region"
                            aria-labelledby="cml-low-risk"
                          >
                            <h4 id="cml-low-risk">
                              Chronic phase - low risk: 1st line
                            </h4>
                            <ul class="regimen-list">
                              <li>Imatinib</li>
                            </ul>
                          </div>

                          <div
                            class="sub-col"
                            role="region"
                            aria-labelledby="cml-high-risk"
                          >
                            <h4 id="cml-high-risk">
                              Chronic phase - intermediate or high risk: 1st
                              line
                            </h4>
                            <ul class="regimen-list">
                              <li>Dasatinib</li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- --- Colorectal Cancer (level-3) --- -->
                  <div
                    class="accordion group level-3"
                    aria-label="Colorectal Cancer"
                  >
                    <input type="checkbox" id="lvl3-colorectal" />
                    <label
                      class="head"
                      for="lvl3-colorectal"
                      role="button"
                      aria-controls="panel-lvl3-colorectal"
                      aria-expanded="false"
                      tabindex="0"
                    >
                      <div class="left">
                        <div
                          style="
                            width: 6px;
                            height: 28px;
                            
                            margin-right: 8px;
                          "
                        ></div>
                        <div>
                          <h3>Colorectal Cancer</h3>
                          <div class="small-sub">
                            Metastatic KRAS/NRAS/BRAF status
                          </div>
                        </div>
                      </div>
                      <svg class="chev" viewBox="0 0 24 24" aria-hidden="true">
                        <path
                          fill="currentColor"
                          d="M9.29 6.71a1 1 0 011.42 0L15 12l-4.29 4.29a1 1 0 01-1.42-1.42L12.17 12 9.29 8.12a1 1 0 010-1.41z"
                        />
                      </svg>
                    </label>

                    <div
                      class="content"
                      id="panel-lvl3-colorectal"
                      role="region"
                      aria-labelledby="lvl3-colorectal"
                    >
                      <div class="panel-body">
                        <!-- Two-column: WT/Mutant vs WT (colon left sided) -->
                        <div class="sub-grid">
                          <div
                            class="sub-col"
                            role="region"
                            aria-labelledby="colorectal-mutant"
                          >
                            <h4 id="colorectal-mutant">
                              Metastatic KRAS/NRAS/BRAF - WT/Mutant: 1st line
                            </h4>
                            <ul class="regimen-list">
                              <li>
                                Capecitabine + Oxaliplatin ± bevacizumab**
                              </li>
                            </ul>
                          </div>

                          <div
                            class="sub-col"
                            role="region"
                            aria-labelledby="colorectal-wt"
                          >
                            <h4 id="colorectal-wt">
                              Metastatic KRAS/NRAS/BRAF - WT (colon left sided
                              tumors only): 1st line
                            </h4>
                            <ul class="regimen-list">
                              <li>Capecitabine + Oxaliplatin + cetuximab</li>
                            </ul>
                          </div>
                        </div>

                        <!-- Notes section -->
                        <div class="notes">
                          <p>*Use of TOI value-based preferred bevacizumab product is required</p>
                          **Bevacizumab is considered appropriate for patients who are: age &lt;65, controlled hypertension, no pre-existing proteinuria, not currently or at high risk of bleeding, not high risk for arterial or VTE events, and did not have surgery within the past 28 days
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- --- Head and Neck Cancer (level-3) --- -->
                  <div
                    class="accordion group level-3"
                    aria-label="Head and Neck Cancer"
                  >
                    <input type="checkbox" id="lvl3-head-neck" />
                    <label
                      class="head"
                      for="lvl3-head-neck"
                      role="button"
                      aria-controls="panel-lvl3-head-neck"
                      aria-expanded="false"
                      tabindex="0"
                    >
                      <div class="left">
                        <div
                          style="
                            width: 6px;
                            height: 28px;
                            
                            margin-right: 8px;
                          "
                        ></div>
                        <div>
                          <h3>Head and Neck Cancer</h3>
                          <div class="small-sub">
                            Squamous Cell and Nasopharyngeal
                          </div>
                        </div>
                      </div>
                      <svg class="chev" viewBox="0 0 24 24" aria-hidden="true">
                        <path
                          fill="currentColor"
                          d="M9.29 6.71a1 1 0 011.42 0L15 12l-4.29 4.29a1 1 0 01-1.42-1.42L12.17 12 9.29 8.12a1 1 0 010-1.41z"
                        />
                      </svg>
                    </label>

                    <div
                      class="content"
                      id="panel-lvl3-head-neck"
                      role="region"
                      aria-labelledby="lvl3-head-neck"
                    >
                      <div class="panel-body">
                        <!-- Squamous Cell Cancer Section -->
                        <div class="sub-grid">
                          <div
                            class="sub-col"
                            role="region"
                            aria-labelledby="squamous-locally-advanced"
                          >
                            <h4 id="squamous-locally-advanced">
                              Squamous Cell Cancer (Non-nasopharyngeal) -
                              Locally Advanced: 1st line
                            </h4>
                            <ul class="regimen-list">
                              <li>Cisplatin (high dose or weekly) + XRT</li>
                            </ul>
                          </div>

                          <div
                            class="sub-col"
                            role="region"
                            aria-labelledby="squamous-recurrent"
                          >
                            <h4 id="squamous-recurrent">
                              Squamous Cell Cancer (Non-nasopharyngeal) -
                              Recurrent or Metastatic:
                            </h4>
                            <ul class="regimen-list">
                              <li>Pembrolizumab (CPS ≥1)</li>
                              <li>Pembrolizumab ± Platinum + 5-FU</li>
                            </ul>
                          </div>
                        </div>

                        <!-- Nasopharyngeal Section -->
                        <div style="margin-top: 16px">
                          <div class="sub-grid three-col">
                            <div
                              class="sub-col"
                              role="region"
                              aria-labelledby="nasopharyngeal-locally"
                            >
                              <h4 id="nasopharyngeal-locally">
                                Nasopharyngeal Carcinoma - Locally Advanced
                              </h4>
                              <ul class="regimen-list">
                                <li>
                                  Gemcitabine + cisplatin x 3 cycles followed by
                                  Platinum + XRT (if OK for induction)
                                </li>
                                <li>
                                  Cisplatin + XRT followed by Cisplatin + 5-FU
                                </li>
                              </ul>
                            </div>

                            <div
                              class="sub-col"
                              role="region"
                              aria-labelledby="nasopharyngeal-recurrent-1st"
                            >
                              <h4 id="nasopharyngeal-recurrent-1st">
                                Nasopharyngeal Carcinoma - Recurrent or
                                Metastatic: 1st line
                              </h4>
                              <ul class="regimen-list">
                                <li>Gemcitabine + Cisplatin ± Toripalimab</li>
                              </ul>
                            </div>

                            <div
                              class="sub-col"
                              role="region"
                              aria-labelledby="nasopharyngeal-recurrent-2nd"
                            >
                              <h4 id="nasopharyngeal-recurrent-2nd">
                                Nasopharyngeal Carcinoma - Recurrent or
                                Metastatic: 2nd line
                              </h4>
                              <ul class="regimen-list">
                                <li>
                                  Toripalimab (if previously treated with
                                  platinum)
                                </li>
                              </ul>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- --- Hepatocellular Carcinoma (level-3) --- -->
                  <div
                    class="accordion group level-3"
                    aria-label="Hepatocellular Carcinoma"
                  >
                    <input type="checkbox" id="lvl3-hepatocellular" />
                    <label
                      class="head"
                      for="lvl3-hepatocellular"
                      role="button"
                      aria-controls="panel-lvl3-hepatocellular"
                      aria-expanded="false"
                      tabindex="0"
                    >
                      <div class="left">
                        <div
                          style="
                            width: 6px;
                            height: 28px;
                            
                            margin-right: 8px;
                          "
                        ></div>
                        <div>
                          <h3>Hepatocellular Carcinoma</h3>
                          <div class="small-sub">
                            Unresectable/Metastatic treatment lines
                          </div>
                        </div>
                      </div>
                      <svg class="chev" viewBox="0 0 24 24" aria-hidden="true">
                        <path
                          fill="currentColor"
                          d="M9.29 6.71a1 1 0 011.42 0L15 12l-4.29 4.29a1 1 0 01-1.42-1.42L12.17 12 9.29 8.12a1 1 0 010-1.41z"
                        />
                      </svg>
                    </label>

                    <div
                      class="content"
                      id="panel-lvl3-hepatocellular"
                      role="region"
                      aria-labelledby="lvl3-hepatocellular"
                    >
                      <div class="panel-body">
                        <div class="sub-grid">
                          <div
                            class="sub-col"
                            role="region"
                            aria-labelledby="hepatocellular-1st"
                          >
                            <h4 id="hepatocellular-1st">
                              Unresectable/Metastatic: 1st line
                            </h4>
                            <ul class="regimen-list">
                              <li>Tremelimumab + durvalumab</li>
                            </ul>
                          </div>

                          <div
                            class="sub-col"
                            role="region"
                            aria-labelledby="hepatocellular-2nd"
                          >
                            <h4 id="hepatocellular-2nd">
                              Unresectable/Metastatic: 2nd line
                            </h4>
                            <ul class="regimen-list">
                              <li>Cabozantinib</li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- --- Kidney Cancer (level-3) --- -->
                  <div
                    class="accordion group level-3"
                    aria-label="Kidney Cancer"
                  >
                    <input type="checkbox" id="lvl3-kidney" />
                    <label
                      class="head"
                      for="lvl3-kidney"
                      role="button"
                      aria-controls="panel-lvl3-kidney"
                      aria-expanded="false"
                      tabindex="0"
                    >
                      <div class="left">
                        <div
                          style="
                            width: 6px;
                            height: 28px;
                            
                            margin-right: 8px;
                          "
                        ></div>
                        <div>
                          <h3>Kidney Cancer</h3>
                          <div class="small-sub">
                            Advanced/Metastatic - Clear Cell Histology
                          </div>
                        </div>
                      </div>
                      <svg class="chev" viewBox="0 0 24 24" aria-hidden="true">
                        <path
                          fill="currentColor"
                          d="M9.29 6.71a1 1 0 011.42 0L15 12l-4.29 4.29a1 1 0 01-1.42-1.42L12.17 12 9.29 8.12a1 1 0 010-1.41z"
                        />
                      </svg>
                    </label>

                    <div
                      class="content"
                      id="panel-lvl3-kidney"
                      role="region"
                      aria-labelledby="lvl3-kidney"
                    >
                      <div class="panel-body">
                        <div
                          class="single-block"
                          role="region"
                          aria-labelledby="kidney-1st"
                        >
                          <h4 id="kidney-1st">
                            Advanced/Metastatic - Clear Cell Histology: 1st line
                          </h4>
                          <ul class="regimen-list">
                            <li>Axitinib + pembrolizumab</li>
                            <li>Lenvatinib + pembrolizumab</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- --- Lung Cancer: Non-Small Cell (level-3) --- -->
                  <div
                    class="accordion group level-3"
                    aria-label="Lung Cancer: Non-Small Cell"
                  >
                    <input type="checkbox" id="lvl3-lung-nsclc" />
                    <label
                      class="head"
                      for="lvl3-lung-nsclc"
                      role="button"
                      aria-controls="panel-lvl3-lung-nsclc"
                      aria-expanded="false"
                      tabindex="0"
                    >
                      <div class="left">
                        <div
                          style="
                            width: 6px;
                            height: 28px;
                            
                            margin-right: 8px;
                          "
                        ></div>
                        <div>
                          <h3>Lung Cancer: Non-Small Cell</h3>
                          <div class="small-sub">
                            Advanced/Metastatic: 1st line treatments
                          </div>
                        </div>
                      </div>
                      <svg class="chev" viewBox="0 0 24 24" aria-hidden="true">
                        <path
                          fill="currentColor"
                          d="M9.29 6.71a1 1 0 011.42 0L15 12l-4.29 4.29a1 1 0 01-1.42-1.42L12.17 12 9.29 8.12a1 1 0 010-1.41z"
                        />
                      </svg>
                    </label>

                    <div
                      class="content"
                      id="panel-lvl3-lung-nsclc"
                      role="region"
                      aria-labelledby="lvl3-lung-nsclc"
                    >
                      <div class="panel-body">
                        <!-- EGFR Exon 19 Deletion or Exon 21 L858R mutations -->
                        <div
                          class="single-block"
                          role="region"
                          aria-labelledby="nsclc-egfr"
                          style="margin-bottom: 16px"
                        >
                          <h4 id="nsclc-egfr">
                            Advanced/Metastatic: 1st line - EGFR Exon 19
                            Deletion or Exon 21 L858R mutations positive
                          </h4>
                          <ul class="regimen-list">
                            <li>Osimertinib</li>
                          </ul>
                        </div>

                        <!-- PD-L1 ≥ 50% treatments -->
                        <div class="sub-grid" style="margin-bottom: 16px">
                          <div
                            class="sub-col"
                            role="region"
                            aria-labelledby="nsclc-pdl1-50-nonsquamous"
                          >
                            <h4 id="nsclc-pdl1-50-nonsquamous">
                              Advanced/Metastatic: 1st line - Non-Squamous,
                              PD-L1 ≥ 50% and biomarker negative
                            </h4>
                            <ul class="regimen-list">
                              <li>Weight-based pembrolizumab</li>
                              <li>Cemiplimab</li>
                            </ul>
                          </div>

                          <div
                            class="sub-col"
                            role="region"
                            aria-labelledby="nsclc-pdl1-50-squamous"
                          >
                            <h4 id="nsclc-pdl1-50-squamous">
                              Advanced/Metastatic: 1st line - Squamous, PD-L1 ≥
                              50% and biomarker negative
                            </h4>
                            <ul class="regimen-list">
                              <li>Weight-based pembrolizumab</li>
                              <li>Cemiplimab</li>
                            </ul>
                          </div>
                        </div>

                        <!-- PD-L1 1-49% treatments -->
                        <div class="sub-grid" style="margin-bottom: 16px">
                          <div
                            class="sub-col"
                            role="region"
                            aria-labelledby="nsclc-pdl1-1-49-nonsquamous"
                          >
                            <h4 id="nsclc-pdl1-1-49-nonsquamous">
                              Advanced/Metastatic: 1st line - Non-Squamous,
                              PD-L1 1-49% and biomarker negative
                            </h4>
                            <ul class="regimen-list">
                              <li>
                                Carboplatin + pemetrexed ± weight-based
                                pembrolizumab
                              </li>
                              <li>Carboplatin + pemetrexed ± cemiplimab</li>
                            </ul>
                          </div>

                          <div
                            class="sub-col"
                            role="region"
                            aria-labelledby="nsclc-pdl1-1-49-squamous"
                          >
                            <h4 id="nsclc-pdl1-1-49-squamous">
                              Advanced/Metastatic: 1st line - Squamous, PD-L1
                              1-49% and biomarker negative
                            </h4>
                            <ul class="regimen-list">
                              <li>
                                Carboplatin + paclitaxel ± weight-based
                                pembrolizumab
                              </li>
                              <li>Carboplatin + paclitaxel ± cemiplimab</li>
                            </ul>
                          </div>
                        </div>

                        <!-- PD-L1 <1% treatments -->
                        <div class="sub-grid">
                          <div
                            class="sub-col"
                            role="region"
                            aria-labelledby="nsclc-pdl1-less1-nonsquamous"
                          >
                            <h4 id="nsclc-pdl1-less1-nonsquamous">
                              Advanced/Metastatic: 1st line - Non-Squamous,
                              PD-L1 <1% and biomarker negative
                            </h4>
                            <ul class="regimen-list">
                              <li>
                                Carboplatin + pemetrexed ± weight-based
                                pembrolizumab
                              </li>
                              <li>Carboplatin + pemetrexed ± cemiplimab</li>
                            </ul>
                          </div>

                          <div
                            class="sub-col"
                            role="region"
                            aria-labelledby="nsclc-pdl1-less1-squamous"
                          >
                            <h4 id="nsclc-pdl1-less1-squamous">
                              Advanced/Metastatic: 1st line - Squamous, PD-L1
                              <1% and biomarker negative
                            </h4>
                            <ul class="regimen-list">
                              <li>
                                Carboplatin + paclitaxel ± weight-based
                                pembrolizumab
                              </li>
                              <li>Carboplatin + paclitaxel ± cemiplimab</li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- --- Lung Cancer: Small Cell (level-3) --- -->
                  <div
                    class="accordion group level-3"
                    aria-label="Lung Cancer: Small Cell"
                  >
                    <input type="checkbox" id="lvl3-lung-sclc" />
                    <label
                      class="head"
                      for="lvl3-lung-sclc"
                      role="button"
                      aria-controls="panel-lvl3-lung-sclc"
                      aria-expanded="false"
                      tabindex="0"
                    >
                      <div class="left">
                        <div
                          style="
                            width: 6px;
                            height: 28px;
                            
                            margin-right: 8px;
                          "
                        ></div>
                        <div>
                          <h3>Lung Cancer: Small Cell</h3>
                          <div class="small-sub">
                            Limited and Extensive Stage
                          </div>
                        </div>
                      </div>
                      <svg class="chev" viewBox="0 0 24 24" aria-hidden="true">
                        <path
                          fill="currentColor"
                          d="M9.29 6.71a1 1 0 011.42 0L15 12l-4.29 4.29a1 1 0 01-1.42-1.42L12.17 12 9.29 8.12a1 1 0 010-1.41z"
                        />
                      </svg>
                    </label>

                    <div
                      class="content"
                      id="panel-lvl3-lung-sclc"
                      role="region"
                      aria-labelledby="lvl3-lung-sclc"
                    >
                      <div class="panel-body">
                        <div class="sub-grid">
                          <div
                            class="sub-col"
                            role="region"
                            aria-labelledby="sclc-limited"
                          >
                            <h4 id="sclc-limited">Limited Stage: 1st line</h4>
                            <ul class="regimen-list">
                              <li>
                                Cisplatin + etoposide x 4 cycles + durvalumab
                                consolidation
                              </li>
                            </ul>
                          </div>

                          <div
                            class="sub-col"
                            role="region"
                            aria-labelledby="sclc-extensive"
                          >
                            <h4 id="sclc-extensive">
                              Extensive Stage: 1st line
                            </h4>
                            <ul class="regimen-list">
                              <li>Carboplatin + etoposide ± durvalumab</li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- --- Lymphoma, Classic Hodgkin (level-3) --- -->
                  <div
                    class="accordion group level-3"
                    aria-label="Lymphoma, Classic Hodgkin"
                  >
                    <input type="checkbox" id="lvl3-hodgkin" />
                    <label
                      class="head"
                      for="lvl3-hodgkin"
                      role="button"
                      aria-controls="panel-lvl3-hodgkin"
                      aria-expanded="false"
                      tabindex="0"
                    >
                      <div class="left">
                        <div
                          style="
                            width: 6px;
                            height: 28px;
                            
                            margin-right: 8px;
                          "
                        ></div>
                        <div>
                          <h3>Lymphoma, Classic Hodgkin</h3>
                          <div class="small-sub">Stage III/IV: 1st line</div>
                        </div>
                      </div>
                      <svg class="chev" viewBox="0 0 24 24" aria-hidden="true">
                        <path
                          fill="currentColor"
                          d="M9.29 6.71a1 1 0 011.42 0L15 12l-4.29 4.29a1 1 0 01-1.42-1.42L12.17 12 9.29 8.12a1 1 0 010-1.41z"
                        />
                      </svg>
                    </label>

                    <div
                      class="content"
                      id="panel-lvl3-hodgkin"
                      role="region"
                      aria-labelledby="lvl3-hodgkin"
                    >
                      <div class="panel-body">
                        <div
                          class="single-block"
                          role="region"
                          aria-labelledby="hodgkin-stage3-4"
                        >
                          <h4 id="hodgkin-stage3-4">Stage III/IV: 1st line</h4>
                          <ul class="regimen-list">
                            <li>
                              Nivolumab + AVD (doxorubicin + dacarbazine +
                              vinblastine)
                            </li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- --- Lymphoma, Diffuse Large B-cell (level-3) --- -->
                  <div
                    class="accordion group level-3"
                    aria-label="Lymphoma, Diffuse Large B-cell"
                  >
                    <input type="checkbox" id="lvl3-dlbcl" />
                    <label
                      class="head"
                      for="lvl3-dlbcl"
                      role="button"
                      aria-controls="panel-lvl3-dlbcl"
                      aria-expanded="false"
                      tabindex="0"
                    >
                      <div class="left">
                        <div
                          style="
                            width: 6px;
                            height: 28px;
                            
                            margin-right: 8px;
                          "
                        ></div>
                        <div>
                          <h3>Lymphoma, Diffuse Large B-cell</h3>
                          <div class="small-sub">1st line therapy</div>
                        </div>
                      </div>
                      <svg class="chev" viewBox="0 0 24 24" aria-hidden="true">
                        <path
                          fill="currentColor"
                          d="M9.29 6.71a1 1 0 011.42 0L15 12l-4.29 4.29a1 1 0 01-1.42-1.42L12.17 12 9.29 8.12a1 1 0 010-1.41z"
                        />
                      </svg>
                    </label>

                    <div
                      class="content"
                      id="panel-lvl3-dlbcl"
                      role="region"
                      aria-labelledby="lvl3-dlbcl"
                    >
                      <div class="panel-body">
                        <div
                          class="single-block"
                          role="region"
                          aria-labelledby="dlbcl-1st"
                        >
                          <h4 id="dlbcl-1st">1st line therapy:</h4>
                          <ul class="regimen-list">
                            <li>
                              RCHOP (rituximab*, cyclophosphamide, doxorubicin,
                              vincristine, and prednisolone)
                            </li>
                          </ul>
                        </div>
                         <!-- Notes section -->
                        <div class="notes">
                          <p>*Use of TOI value-based preferred rituximab product is required</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- --- Merkel Cell Carcinoma (level-3) --- -->
                  <div
                    class="accordion group level-3"
                    aria-label="Merkel Cell Carcinoma"
                  >
                    <input type="checkbox" id="lvl3-merkel" />
                    <label
                      class="head"
                      for="lvl3-merkel"
                      role="button"
                      aria-controls="panel-lvl3-merkel"
                      aria-expanded="false"
                      tabindex="0"
                    >
                      <div class="left">
                        <div
                          style="
                            width: 6px;
                            height: 28px;
                            
                            margin-right: 8px;
                          "
                        ></div>
                        <div>
                          <h3>Merkel Cell Carcinoma</h3>
                          <div class="small-sub">
                            Recurrent/locally advanced/metastatic
                          </div>
                        </div>
                      </div>
                      <svg class="chev" viewBox="0 0 24 24" aria-hidden="true">
                        <path
                          fill="currentColor"
                          d="M9.29 6.71a1 1 0 011.42 0L15 12l-4.29 4.29a1 1 0 01-1.42-1.42L12.17 12 9.29 8.12a1 1 0 010-1.41z"
                        />
                      </svg>
                    </label>

                    <div
                      class="content"
                      id="panel-lvl3-merkel"
                      role="region"
                      aria-labelledby="lvl3-merkel"
                    >
                      <div class="panel-body">
                        <div
                          class="single-block"
                          role="region"
                          aria-labelledby="merkel-1st"
                        >
                          <h4 id="merkel-1st">
                            Recurrent/locally advanced/metastatic - not XRT or
                            surgery candidate: 1st line
                          </h4>
                          <ul class="regimen-list">
                            <li>Pembrolizumab</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- --- Mesothelioma: Pleural (level-3) --- -->
                  <div
                    class="accordion group level-3"
                    aria-label="Mesothelioma: Pleural"
                  >
                    <input type="checkbox" id="lvl3-mesothelioma" />
                    <label
                      class="head"
                      for="lvl3-mesothelioma"
                      role="button"
                      aria-controls="panel-lvl3-mesothelioma"
                      aria-expanded="false"
                      tabindex="0"
                    >
                      <div class="left">
                        <div
                          style="
                            width: 6px;
                            height: 28px;
                            
                            margin-right: 8px;
                          "
                        ></div>
                        <div>
                          <h3>Mesothelioma: Pleural</h3>
                          <div class="small-sub">1st line therapy</div>
                        </div>
                      </div>
                      <svg class="chev" viewBox="0 0 24 24" aria-hidden="true">
                        <path
                          fill="currentColor"
                          d="M9.29 6.71a1 1 0 011.42 0L15 12l-4.29 4.29a1 1 0 01-1.42-1.42L12.17 12 9.29 8.12a1 1 0 010-1.41z"
                        />
                      </svg>
                    </label>

                    <div
                      class="content"
                      id="panel-lvl3-mesothelioma"
                      role="region"
                      aria-labelledby="lvl3-mesothelioma"
                    >
                      <div class="panel-body">
                        <div
                          class="single-block"
                          role="region"
                          aria-labelledby="mesothelioma-1st"
                        >
                          <h4 id="mesothelioma-1st">1st line therapy:</h4>
                          <ul class="regimen-list">
                            <li>(Cisplatin or carboplatin) + pemetrexed</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- --- Ovarian Cancer (level-3) --- -->
                  <div
                    class="accordion group level-3"
                    aria-label="Ovarian Cancer"
                  >
                    <input type="checkbox" id="lvl3-ovarian" />
                    <label
                      class="head"
                      for="lvl3-ovarian"
                      role="button"
                      aria-controls="panel-lvl3-ovarian"
                      aria-expanded="false"
                      tabindex="0"
                    >
                      <div class="left">
                        <div
                          style="
                            width: 6px;
                            height: 28px;
                            
                            margin-right: 8px;
                          "
                        ></div>
                        <div>
                          <h3>Ovarian Cancer</h3>
                          <div class="small-sub">
                            Low Risk vs High Risk treatment
                          </div>
                        </div>
                      </div>
                      <svg class="chev" viewBox="0 0 24 24" aria-hidden="true">
                        <path
                          fill="currentColor"
                          d="M9.29 6.71a1 1 0 011.42 0L15 12l-4.29 4.29a1 1 0 01-1.42-1.42L12.17 12 9.29 8.12a1 1 0 010-1.41z"
                        />
                      </svg>
                    </label>

                    <div
                      class="content"
                      id="panel-lvl3-ovarian"
                      role="region"
                      aria-labelledby="lvl3-ovarian"
                    >
                      <div class="panel-body">
                        <div class="sub-grid">
                          <div
                            class="sub-col"
                            role="region"
                            aria-labelledby="ovarian-low-risk"
                          >
                            <h4 id="ovarian-low-risk">Low Risk: 1st line</h4>
                            <ul class="regimen-list">
                              <li>Paclitaxel + carboplatin</li>
                            </ul>
                          </div>

                          <div
                            class="sub-col"
                            role="region"
                            aria-labelledby="ovarian-high-risk"
                          >
                            <h4 id="ovarian-high-risk">
                              High Risk (Stage III >1cm residual dz or Stage
                              IV): 1st line
                            </h4>
                            <ul class="regimen-list">
                              <li>Paclitaxel + carboplatin ± bevacizumab**</li>
                            </ul>
                          </div>
                        </div>

                         <!-- Notes section -->
                        <div class="notes">
                          <p>*Use of TOI value-based preferred bevacizumab product is required</p>
                          **Bevacizumab is considered appropriate for patients who are: age &lt;65, controlled hypertension, no pre-existing proteinuria, not currently or at high risk of bleeding, not high risk for arterial or VTE events, and did not have surgery within the past 28 days
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- --- Pancreatic Cancer (level-3) --- -->
                  <div
                    class="accordion group level-3"
                    aria-label="Pancreatic Cancer"
                  >
                    <input type="checkbox" id="lvl3-pancreatic" />
                    <label
                      class="head"
                      for="lvl3-pancreatic"
                      role="button"
                      aria-controls="panel-lvl3-pancreatic"
                      aria-expanded="false"
                      tabindex="0"
                    >
                      <div class="left">
                        <div
                          style="
                            width: 6px;
                            height: 28px;
                            
                            margin-right: 8px;
                          "
                        ></div>
                        <div>
                          <h3>Pancreatic Cancer</h3>
                          <div class="small-sub">
                            Advanced/Metastatic: 1st line
                          </div>
                        </div>
                      </div>
                      <svg class="chev" viewBox="0 0 24 24" aria-hidden="true">
                        <path
                          fill="currentColor"
                          d="M9.29 6.71a1 1 0 011.42 0L15 12l-4.29 4.29a1 1 0 01-1.42-1.42L12.17 12 9.29 8.12a1 1 0 010-1.41z"
                        />
                      </svg>
                    </label>

                    <div
                      class="content"
                      id="panel-lvl3-pancreatic"
                      role="region"
                      aria-labelledby="lvl3-pancreatic"
                    >
                      <div class="panel-body">
                        <div
                          class="single-block"
                          role="region"
                          aria-labelledby="pancreatic-1st"
                        >
                          <h4 id="pancreatic-1st">
                            Advanced/Metastatic: 1st line
                          </h4>
                          <ul class="regimen-list">
                            <li>
                              modified FOLFIRINOX (fluorouracil, leucovorin,
                              irinotecan, and oxaliplatin) (ECOG 0-1)
                            </li>
                            <li>
                              Gemcitabine + albumin-bound paclitaxel (ECOG 0-2)
                            </li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- --- Prostate Cancer (level-3) --- -->
                  <div
                    class="accordion group level-3"
                    aria-label="Prostate Cancer"
                  >
                    <input type="checkbox" id="lvl3-prostate" />
                    <label
                      class="head"
                      for="lvl3-prostate"
                      role="button"
                      aria-controls="panel-lvl3-prostate"
                      aria-expanded="false"
                      tabindex="0"
                    >
                      <div class="left">
                        <div
                          style="
                            width: 6px;
                            height: 28px;
                            
                            margin-right: 8px;
                          "
                        ></div>
                        <div>
                          <h3>Prostate Cancer</h3>
                          <div class="small-sub">
                            Metastatic - Castrate Sensitive and Resistant
                          </div>
                        </div>
                      </div>
                      <svg class="chev" viewBox="0 0 24 24" aria-hidden="true">
                        <path
                          fill="currentColor"
                          d="M9.29 6.71a1 1 0 011.42 0L15 12l-4.29 4.29a1 1 0 01-1.42-1.42L12.17 12 9.29 8.12a1 1 0 010-1.41z"
                        />
                      </svg>
                    </label>

                    <div
                      class="content"
                      id="panel-lvl3-prostate"
                      role="region"
                      aria-labelledby="lvl3-prostate"
                    >
                      <div class="panel-body">
                        <!-- Castrate Sensitive -->
                        <div class="sub-grid" style="margin-bottom: 16px">
                          <div
                            class="sub-col"
                            role="region"
                            aria-labelledby="prostate-cs-low"
                          >
                            <h4 id="prostate-cs-low">
                              Metastatic - Castrate Sensitive - Low Volume: 1st
                              line
                            </h4>
                            <ul class="regimen-list">
                              <li>Leuprorelin + Abiraterone</li>
                            </ul>
                          </div>

                          <div
                            class="sub-col"
                            role="region"
                            aria-labelledby="prostate-cs-high"
                          >
                            <h4 id="prostate-cs-high">
                              Metastatic - Castrate Sensitive - High Volume: 1st
                              line
                            </h4>
                            <ul class="regimen-list">
                              <li>Leuprorelin + Abiraterone + docetaxel</li>
                            </ul>
                          </div>
                        </div>

                        <!-- Castrate Resistant -->
                        <div class="sub-grid" style="margin-bottom: 16px">
                          <div
                            class="sub-col"
                            role="region"
                            aria-labelledby="prostate-cr-1st"
                          >
                            <h4 id="prostate-cr-1st">
                              Metastatic - Castrate Resistant: 1st line
                            </h4>
                            <ul class="regimen-list">
                              <li>Leupropelin + Abiraterone</li>
                            </ul>
                          </div>

                          <div
                            class="sub-col"
                            role="region"
                            aria-labelledby="prostate-cr-2nd"
                          >
                            <h4 id="prostate-cr-2nd">
                              Metastatic - Castrate Resistant: 2nd line
                            </h4>
                            <ul class="regimen-list">
                              <li>Leupropelin + Docetaxel</li>
                            </ul>
                          </div>
                        </div>

                        <!-- PSMA Avid -->
                        <div
                          class="single-block"
                          role="region"
                          aria-labelledby="prostate-psma"
                        >
                          <h4 id="prostate-psma">
                            Metastatic - Castrate Resistant - PSMA Avid:
                          </h4>
                          
                          <ul class="regimen-list">
                            <li>
                              Pluvicto (Must have tried a taxane and oral
                              anti-androgen)
                            </li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- --- Squamous Cell Skin Cancer (level-3) --- -->
                  <div
                    class="accordion group level-3"
                    aria-label="Squamous Cell Skin Cancer"
                  >
                    <input type="checkbox" id="lvl3-squamous-skin" />
                    <label
                      class="head"
                      for="lvl3-squamous-skin"
                      role="button"
                      aria-controls="panel-lvl3-squamous-skin"
                      aria-expanded="false"
                      tabindex="0"
                    >
                      <div class="left">
                        <div
                          style="
                            width: 6px;
                            height: 28px;
                            
                            margin-right: 8px;
                          "
                        ></div>
                        <div>
                          <h3>Squamous Cell Skin Cancer</h3>
                          <div class="small-sub">
                            Recurrent/Locally Advanced/Metastatic
                          </div>
                        </div>
                      </div>
                      <svg class="chev" viewBox="0 0 24 24" aria-hidden="true">
                        <path
                          fill="currentColor"
                          d="M9.29 6.71a1 1 0 011.42 0L15 12l-4.29 4.29a1 1 0 01-1.42-1.42L12.17 12 9.29 8.12a1 1 0 010-1.41z"
                        />
                      </svg>
                    </label>

                    <div
                      class="content"
                      id="panel-lvl3-squamous-skin"
                      role="region"
                      aria-labelledby="lvl3-squamous-skin"
                    >
                      <div class="panel-body">
                        <div
                          class="single-block"
                          role="region"
                          aria-labelledby="squamous-skin-1st"
                        >
                          <h4 id="squamous-skin-1st">
                            Recurrent/Locally Advanced/Metastatic - Not XRT or
                            Surgery Candidate: 1st line
                          </h4>
                          <ul class="regimen-list">
                            <li>Cemiplimab</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- end lvl2 panel-body -->
              </div>
              <!-- end lvl2 content -->
            </div>
            <!-- end value based pathways group -->

            <!-- SUB-ACCORDION: Traditional Pathways (level-2) -->
            <div
              class="accordion group level-2 parent-border"
              aria-label="Traditional Pathways"
              style="margin-bottom: 0px;"
            >
              <input type="checkbox" id="lvl2-traditional" />
              <label
                class="head"
                for="lvl2-traditional"
                role="button"
                aria-controls="panel-lvl2-traditional"
                aria-expanded="false"
                tabindex="0"
              >
                <div class="triangle"></div>
                <h3>Traditional Pathways</h3>
              </label>
            </div>
            <!-- end traditional pathways group -->
          </div>
          <!-- end lvl1 panel-body -->
        </div>
        <!-- end lvl1 content -->
      </section>

      <!-- TOP LEVEL: Formulary (level-1) -->
      <section class="accordion level-1 parent-border" aria-label="Formulary">
        <input type="checkbox" id="lvl1-formulary" />
        <label
          class="head"
          for="lvl1-formulary"
          role="button"
          aria-controls="panel-lvl1-formulary"
          aria-expanded="false"
          tabindex="0"
        >
          <div class="triangle"></div>
          <h3>Formulary</h3>
        </label>

        <div
          class="content"
          id="panel-lvl1-formulary"
          role="region"
          aria-labelledby="lvl1-formulary"
        >
          <div class="panel-body">
            <!-- SUB-ACCORDION: Value-Based Formulary (level-2) -->
            <div
              class="accordion group level-2 parent-border"
              aria-label="Value-Based Formulary"
            >
              <input type="checkbox" id="lvl2-formulary-value" checked />
              <label
                class="head"
                for="lvl2-formulary-value"
                role="button"
                aria-controls="panel-lvl2-formulary-value"
                aria-expanded="false"
                tabindex="0"
              >
                <div class="triangle"></div>
                <h3>Value-Based</h3>
              </label>

              <div
                class="content"
                id="panel-lvl2-formulary-value"
                role="region"
                aria-labelledby="lvl2-formulary-value"
              >
                <div class="panel-body">
                  <!-- Value-Based Formulary Content - Two Column Layout -->
                  <div class="sub-grid">
                    <!-- Left Column -->
                    <div class="sub-col" role="region" aria-labelledby="value-based-left">
                      <h4>Anti-CD20</h4>
                      <ul>
                        <li>rituximab-pvvr (Ruxience)</li>
                      </ul>

                      <h4>Anti-HER2 - Herceptin</h4>
                      <ul>
                        <li>trastuzumab-dttb (Ontruzant)</li>
                      </ul>

                      <h4>Bendamustine</h4>
                      <ul>
                        <li>bendamustine (Treanda)</li>
                      </ul>

                      <h4>Bortezomibs</h4>
                      <ul>
                        <li>bortezomib (Velcade) generic</li>
                      </ul>

                      <h4>G-CSF (Long acting)</h4>
                      <ul>
                        <li>pegfilgrastim-jmdb (Fulphila)</li>
                      </ul>

                      <h4>Infliximabs</h4>
                      <ul>
                        <li>infliximab (Remicade) BRAND</li>
                      </ul>

                      <h4>LHRH Agonists</h4>
                      <ul>
                        <li>leuprolide acetate (Eligard)</li>
                      </ul>
                    </div>

                    <!-- Right Column -->
                    <div class="sub-col" role="region" aria-labelledby="value-based-right">
                      <h4>Anti - CD38</h4>
                      <ul>
                        <li>daratumumab inj (Darzalex)</li>
                      </ul>

                      <h4>Anti-Metabolite - Alimta</h4>
                      <ul>
                        <li>pemetrexed (Alimta) generic</li>
                      </ul>

                      <h4>Bone Health - Metastasis</h4>
                      <ul>
                        <li>zoledronic acid (Zometa) 4MG</li>
                      </ul>

                      <h4>ESA</h4>
                      <ul>
                        <li>epoetin alfa-epbx (Retacrit)</li>
                      </ul>

                      <h4>G-CSF (Short acting)</h4>
                      <ul>
                        <li>filgrastim-aafi (Nivestym)</li>
                      </ul>

                      <h4>Iron</h4>
                      <ul>
                        <li>iron dextran (INFeD)</li>
                      </ul>

                      <h4>Somatostatin analogs</h4>
                      <ul>
                        <li>octreotide acetate, mi-spheres (Sandostatin Lar Depot) 20MG</li>
                      </ul>
                    </div>
                  </div>

                  <!-- Additional sections in single column -->
                  <div class="sub-grid">
                    <div class="sub-col" role="region">
                      <h4>Anti-EGFR</h4>
                      <ul>
                        <li>Cetuximab (Erbitux)</li>
                      </ul>

                      <h4>Bone Health - Osteoporosis</h4>
                      <ul>
                        <li>zoledronic acid (Reclast) 5MG</li>
                        <li>pamidronate (Aredia)</li>
                      </ul>

                      <h4>Immune Checkpoint Inhibitor</h4>
                      <ul>
                        <li>Cemiplimab (Libtayo) - when clinically appropriate</li>
                        <li>Pembrolizumab- when cemiplimab not indicated</li>
                      </ul>
                    </div>

                    <div class="sub-col" role="region">
                      <h4>Anti-VEGF</h4>
                      <ul>
                        <li>bevacizumab-bvzr (Zirabev)</li>
                      </ul>

                      <h4>Fulvestrant</h4>
                      <ul>
                        <li>fulvestrant (Faslodex)</li>
                      </ul>

                      <h4>IVIG</h4>
                      <ul>
                        <li>immune globulin g(igg)/malt/iga ov (Octagam)</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <!-- end value based formulary group -->

            <!-- SUB-ACCORDION: Traditional Formulary (level-2) -->
            <div
              class="accordion group level-2 parent-border"
              aria-label="Traditional Formulary"
              style="margin-bottom: 0px;"
            >
              <input type="checkbox" id="lvl2-formulary-traditional" />
              <label
                class="head"
                for="lvl2-formulary-traditional"
                role="button"
                aria-controls="panel-lvl2-formulary-traditional"
                aria-expanded="false"
                tabindex="0"
              >
                <div class="triangle"></div>
                <h3>Traditional</h3>
              </label>

              <div
                class="content"
                id="panel-lvl2-formulary-traditional"
                role="region"
                aria-labelledby="lvl2-formulary-traditional"
              >
                <div class="panel-body">
                  <!-- Traditional Formulary Content - Two Column Layout -->
                  <div class="sub-grid">
                    <!-- Left Column -->
                    <div class="sub-col" role="region" aria-labelledby="traditional-left">
                      <h4>Anti-CD20</h4>
                      <ul>
                        <li>rituximab-abbs (Truxima)</li>
                        <li>rituximab-arrx (Riabni)</li>
                      </ul>

                      <h4>Anti-HER2 - Herceptin</h4>
                      <ul>
                        <li>trastuzumab (Herceptin)</li>
                        <li>trastuzumab-hyaluronidase-oysk (Herceptin Hylecta)</li>
                        <li>trastuzumab (Hercessi)</li>
                        <li>trastuzumab-dkst (Ogivri)</li>
                        <li>trastuzumab-pkrb lyo pwd (Herzuma)</li>
                      </ul>

                      <h4>Bendamustine</h4>
                      <ul>
                        <li>bendamustine hcl (Bendeka)</li>
                      </ul>

                      <h4>G-CSF (Long acting)</h4>
                      <ul>
                        <li>eflapegrastim-xnst (Rolvedon)</li>
                        <li>pegfilgrastim-jmdb (Fulphila)</li>
                      </ul>

                      <h4>infliximab</h4>
                      <ul>
                        <li>infliximab (Remicade) BRAND</li>
                      </ul>

                      <h4>LHRH Agonists</h4>
                      <ul>
                        <li>triptorelin pamoate (Trelstar)</li>
                        <li>leuprolide acetate (Eligard)</li>
                        <li>leuprolide acetate (Lupron Depot) PART D benefit</li>
                        <li>leuprolide acetate (Lupron Depot) 7.5mg</li>
                      </ul>
                    </div>

                    <!-- Right Column -->
                    <div class="sub-col" role="region" aria-labelledby="traditional-right">
                      <h4>Anti - CD38</h4>
                      <ul>
                        <li>daratumumab-hyaluronidase-fihj (Darzalex Faspro)</li>
                      </ul>

                      <h4>Anti-Metabolite - Alimta</h4>
                      <ul>
                        <li>pemetrexed (Pemfexy)</li>
                        <li>pemetrexed (Alimta) generic</li>
                      </ul>

                      <h4>Bone Health - Metastasis</h4>
                      <ul>
                        <li>WYOST 120MG/1.7ML</li>
                        <li>denosumab (Xgeva)</li>
                      </ul>

                      <h4>G-CSF (Short acting)</h4>
                      <ul>
                        <li>sargramostim (Leukine)</li>
                        <li>filgrastim-aafi (Nivestym)</li>
                      </ul>

                      <h4>Iron</h4>
                      <ul>
                        <li>ferric derisomaltose (Monoferric)</li>
                        <li>ferric carboxymaltose (Injectafer)</li>
                        <li>iron dextran (INFeD)</li>
                      </ul>

                      <h4>Somatostatin analogs</h4>
                      <ul>
                        <li>octreotide acetate, mi-spheres (Sandostatin Lar Depot) 20MG</li>
                      </ul>
                    </div>
                  </div>

                  <!-- Additional sections in second row -->
                  <div class="sub-grid">
                    <div class="sub-col" role="region">
                      <h4>Anti-EGFR</h4>
                      <ul>
                        <li>Cetuximab (Erbitux)</li>
                      </ul>

                      <h4>Bone Health - Osteoporosis</h4>
                      <ul>
                        <li>JUBBONTI 60MG/ML</li>
                        <li>denosumab (Prolia)</li>
                      </ul>

                      <h4>Immune Checkpoint Inhibitor</h4>
                      <ul>
                        <li>Tecentriq Hybreza - when clinically appropriate</li>
                      </ul>
                    </div>

                    <div class="sub-col" role="region">
                      <h4>Anti-VEGF</h4>
                      <ul>
                        <li>bevacizumab-adcd (Vegzelma)</li>
                        <li>bevacizumab-maly (Alymsys)</li>
                      </ul>

                      <h4>Bortezomibs</h4>
                      <ul>
                        <li>bortezomib (Velcade) generic J</li>
                      </ul>

                      <h4>ESA</h4>
                      <ul>
                        <li>epoetin alfa-epbx (Retacrit)</li>
                      </ul>

                      <h4>Fulvestrant</h4>
                      <ul>
                        <li>fulvestrant (Faslodex)</li>
                      </ul>

                      <h4>IVIG</h4>
                      <ul>
                        <li>immune globulin intravenous (human) – ifas liquid preparation (Panzyga)</li>
                        <li>immune globulin g(igg)/malt/iga ov (Octagam)</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <!-- end traditional formulary group -->
          </div>
          <!-- end lvl1 panel-body -->
        </div>
        <!-- end lvl1 content -->
      </section>

      <!-- Border line after Formulary -->
      <div class="sep" aria-hidden="true"></div>
    </main>
    <!-- end main container -->

    <script>
      // Dynamic breadcrumb functionality
      function updateBreadcrumb() {
        const breadcrumbCurrent = document.querySelector(".breadcrumb-current");

        // Check if Pathways is open
        const pathways = document.querySelector("#lvl1-pathways");
        const formulary = document.querySelector("#lvl1-formulary");

        if (formulary && formulary.checked) {
          // Formulary is open
          breadcrumbCurrent.textContent = "Formulary";
        } else if (pathways && pathways.checked) {
          // Pathways is open, check deeper levels
          const valueBased = document.querySelector("#lvl2-value");
          const traditional = document.querySelector("#lvl2-traditional");

          if (valueBased && valueBased.checked) {
            // Value Based Pathways is open, check for cancer types
            const level3Accordions = document.querySelectorAll(
              '.accordion.level-3 input[type="checkbox"]'
            );
            let currentOpenedCancer = null;

            level3Accordions.forEach((checkbox) => {
              if (checkbox.checked) {
                const label = checkbox.nextElementSibling;
                const h3 = label.querySelector("h3");
                if (h3) {
                  currentOpenedCancer = h3.textContent.trim();
                }
              }
            });

            if (currentOpenedCancer) {
              breadcrumbCurrent.textContent = currentOpenedCancer + " Pathways";
            } else {
              breadcrumbCurrent.textContent = "Value-based Pathways";
            }
          } else if (traditional && traditional.checked) {
            // Traditional Pathways is open
            breadcrumbCurrent.textContent = "Traditional Pathways";
          } else {
            // Only Pathways is open
            breadcrumbCurrent.textContent = "Pathways";
          }
        } else {
          // Nothing is open, default to Pathways
          breadcrumbCurrent.textContent = "Pathways";
        }
      }

      // Fix all old-style headers on page load
      function fixHeaders() {
        const oldHeaders = document.querySelectorAll(
          ".accordion.level-3 .head"
        );
        oldHeaders.forEach((header) => {
          const leftDiv = header.querySelector(".left");
          if (leftDiv) {
            // Extract the h3 text
            const h3 = leftDiv.querySelector("h3");
            const h3Text = h3 ? h3.textContent.trim() : "";

            // Replace the entire header content with new structure
            header.innerHTML = `
              <div class="triangle"></div>
              <h3>${h3Text}</h3>
            `;
          }
        });
      }

      // Track the most recently opened accordion
      let lastOpenedAccordion = null;

      // Listen for accordion changes
      document.addEventListener("change", function (e) {
        if (e.target.type === "checkbox") {
          const accordion = e.target.closest(".accordion");

          if (e.target.checked) {
            // Store the most recently opened accordion
            if (accordion.classList.contains("level-3")) {
              lastOpenedAccordion = e.target.id;
            }
          } else {
            // If the currently tracked accordion is closed, clear it
            if (lastOpenedAccordion === e.target.id) {
              lastOpenedAccordion = null;
            }
          }

          updateBreadcrumbWithTracking();
        }
      });

      // Enhanced breadcrumb update with tracking
      function updateBreadcrumbWithTracking() {
        const breadcrumbCurrent = document.querySelector(".breadcrumb-current");

        // If we have a tracked accordion, use it
        if (lastOpenedAccordion) {
          const checkbox = document.querySelector("#" + lastOpenedAccordion);
          if (checkbox && checkbox.checked) {
            const label = checkbox.nextElementSibling;
            const h3 = label.querySelector("h3");
            if (h3) {
              breadcrumbCurrent.textContent =
                h3.textContent.trim() + " Pathways";
              return;
            }
          }
        }

        // Fallback to regular logic
        updateBreadcrumb();
      }

      // Initialize on page load
      document.addEventListener("DOMContentLoaded", function () {
        fixHeaders();
        updateBreadcrumbWithTracking();
      });
    </script>
  </body>
</html>
